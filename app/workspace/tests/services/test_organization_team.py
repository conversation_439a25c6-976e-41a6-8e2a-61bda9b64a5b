import uuid
from types import SimpleNamespace

from app.auth.repository import UserRepository
from app.workspace.repositories.organization import OrganizationRepository
from app.workspace.repositories.organization_member import OrganizationMemberRepository
from app.workspace.services.organization_team import OrganizationTeamService


def test_get_team_user_ids_with_owner_as_member(mocker):
    org_id = uuid.uuid4()
    user1_id = uuid.uuid4()
    user2_id = uuid.uuid4()
    owner_id = uuid.uuid4()

    mock_org_member_repo = mocker.Mock(spec=OrganizationMemberRepository)
    mock_org_repo = mocker.Mock(spec=OrganizationRepository)
    mock_user_repo = mocker.Mock(spec=UserRepository)
    mock_db_session = mocker.Mock()

    mock_org = SimpleNamespace(
        id=org_id,
        owner_id=owner_id,
    )
    mock_org_repo.get_by_id.return_value = mock_org

    mock_members = [
        SimpleNamespace(user_id=user1_id),
        SimpleNamespace(user_id=user2_id),
        SimpleNamespace(user_id=owner_id),
    ]
    mock_org_member_repo.get_by_organization_id.return_value = mock_members

    service = OrganizationTeamService(
        db_session=mock_db_session,
        org_member_repo=mock_org_member_repo,
        org_repo=mock_org_repo,
        user_repo=mock_user_repo,
    )

    result = service.get_team_user_ids(org_id)

    assert len(result) == 3
    assert user1_id in result
    assert user2_id in result
    assert owner_id in result
    mock_org_repo.get_by_id.assert_called_once_with(org_id)
    mock_org_member_repo.get_by_organization_id.assert_called_once_with(org_id)


def test_get_team_user_ids_with_owner_not_member(mocker):
    org_id = uuid.uuid4()
    user1_id = uuid.uuid4()
    user2_id = uuid.uuid4()
    owner_id = uuid.uuid4()

    mock_org_member_repo = mocker.Mock(spec=OrganizationMemberRepository)
    mock_org_repo = mocker.Mock(spec=OrganizationRepository)
    mock_user_repo = mocker.Mock(spec=UserRepository)
    mock_db_session = mocker.Mock()

    mock_org = SimpleNamespace(
        id=org_id,
        owner_id=owner_id,
    )
    mock_org_repo.get_by_id.return_value = mock_org

    mock_members = [
        SimpleNamespace(user_id=user1_id),
        SimpleNamespace(user_id=user2_id),
    ]
    mock_org_member_repo.get_by_organization_id.return_value = mock_members

    service = OrganizationTeamService(
        db_session=mock_db_session,
        org_member_repo=mock_org_member_repo,
        org_repo=mock_org_repo,
        user_repo=mock_user_repo,
    )

    result = service.get_team_user_ids(org_id)

    assert len(result) == 3
    assert user1_id in result
    assert user2_id in result
    assert owner_id in result
    mock_org_repo.get_by_id.assert_called_once_with(org_id)
    mock_org_member_repo.get_by_organization_id.assert_called_once_with(org_id)


def test_get_team_user_ids_no_members(mocker):
    org_id = uuid.uuid4()
    owner_id = uuid.uuid4()

    mock_org_member_repo = mocker.Mock(spec=OrganizationMemberRepository)
    mock_org_repo = mocker.Mock(spec=OrganizationRepository)
    mock_user_repo = mocker.Mock(spec=UserRepository)
    mock_db_session = mocker.Mock()

    mock_org = SimpleNamespace(
        id=org_id,
        owner_id=owner_id,
    )
    mock_org_repo.get_by_id.return_value = mock_org

    mock_org_member_repo.get_by_organization_id.return_value = []

    service = OrganizationTeamService(
        db_session=mock_db_session,
        org_member_repo=mock_org_member_repo,
        org_repo=mock_org_repo,
        user_repo=mock_user_repo,
    )

    result = service.get_team_user_ids(org_id)

    assert len(result) == 1
    assert owner_id in result
    mock_org_repo.get_by_id.assert_called_once_with(org_id)
    mock_org_member_repo.get_by_organization_id.assert_called_once_with(org_id)


def test_get_team_user_ids_no_org(mocker):
    org_id = uuid.uuid4()

    mock_org_member_repo = mocker.Mock(spec=OrganizationMemberRepository)
    mock_org_repo = mocker.Mock(spec=OrganizationRepository)
    mock_user_repo = mocker.Mock(spec=UserRepository)
    mock_db_session = mocker.Mock()

    mock_org_repo.get_by_id.return_value = None

    mock_org_member_repo.get_by_organization_id.return_value = []

    service = OrganizationTeamService(
        db_session=mock_db_session,
        org_member_repo=mock_org_member_repo,
        org_repo=mock_org_repo,
        user_repo=mock_user_repo,
    )

    result = service.get_team_user_ids(org_id)

    assert len(result) == 0
    mock_org_repo.get_by_id.assert_called_once_with(org_id)
    mock_org_member_repo.get_by_organization_id.assert_called_once_with(org_id)


def test_get_team_member_profile(mocker):
    user_id = uuid.uuid4()
    org_id = uuid.uuid4()

    mock_org_member_repo = mocker.Mock(spec=OrganizationMemberRepository)
    mock_org_repo = mocker.Mock(spec=OrganizationRepository)
    mock_user_repo = mocker.Mock(spec=UserRepository)
    mock_db_session = mocker.Mock()

    mock_member = SimpleNamespace(
        id=uuid.uuid4(),
        user_id=user_id,
        organization_id=org_id,
        is_admin=True,
    )
    mock_org_member_repo.get_by_user_id.return_value = mock_member

    mock_user = SimpleNamespace(
        id=user_id,
        first_name="John",
        last_name="Doe",
        email="<EMAIL>",
    )
    mock_user_repo.get_by_id.return_value = mock_user
    service = OrganizationTeamService(
        db_session=mock_db_session,
        org_member_repo=mock_org_member_repo,
        org_repo=mock_org_repo,
        user_repo=mock_user_repo,
    )
    profile = service.get_team_member_profile(user_id)
    assert profile.id == mock_member.id
    assert profile.user_id == mock_member.user_id
    assert profile.organization_id == mock_member.organization_id
    assert profile.first_name == mock_user.first_name
    assert profile.last_name == mock_user.last_name
    assert profile.email == mock_user.email
    assert profile.is_admin == mock_member.is_admin


def get_team_member_profiles(mocker):
    org_id = uuid.uuid4()
    user1_id = uuid.uuid4()
    user2_id = uuid.uuid4()

    mock_org_member_repo = mocker.Mock(spec=OrganizationMemberRepository)
    mock_org_repo = mocker.Mock(spec=OrganizationRepository)
    mock_user_repo = mocker.Mock(spec=UserRepository)
    mock_db_session = mocker.Mock()

    mock_members = [
        SimpleNamespace(user_id=user1_id, organization_id=org_id, is_admin=True),
        SimpleNamespace(user_id=user2_id, organization_id=org_id, is_admin=False),
    ]
    mock_org_member_repo.get_by_organization_id.return_value = mock_members

    mock_users = [
        SimpleNamespace(
            id=user1_id, first_name="Alice", last_name="Smith", email="<EMAIL>"
        ),
        SimpleNamespace(
            id=user2_id, first_name="Bob", last_name="Jones", email="<EMAIL>"
        ),
    ]
    mock_user_repo.get_by_ids.return_value = mock_users
    service = OrganizationTeamService(
        db_session=mock_db_session,
        org_member_repo=mock_org_member_repo,
        org_repo=mock_org_repo,
        user_repo=mock_user_repo,
    )
    profiles = service.get_team_member_profiles(org_id)
    assert len(profiles) == 2
