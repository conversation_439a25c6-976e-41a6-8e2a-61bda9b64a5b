import datetime
from uuid import uuid4

from app.main import app
from app.workspace.schemas import (
    OrganizationMemberProfile,
    OrganizationMemberRead,
    OrganizationRead,
)


def test_get_member_profiles_admin(
    client, override_organization_team_service, override_organization_service
):
    override_organization_team_service.get_team_member.return_value = (
        OrganizationMemberRead(
            id=uuid4(),
            user_id=uuid4(),
            organization_id=uuid4(),
            is_admin=True,
        )
    )
    organization_id = uuid4()
    override_organization_team_service.get_team_member_profiles.return_value = [
        OrganizationMemberProfile(
            id=uuid4(),
            user_id=uuid4(),
            organization_id=organization_id,
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            email="<EMAIL>",
            is_admin=True,
        ),
        OrganizationMemberProfile(
            id=uuid4(),
            user_id=uuid4(),
            organization_id=organization_id,
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            email="<EMAIL>",
            is_admin=False,
        ),
    ]
    now = datetime.datetime.now(datetime.UTC)
    org = OrganizationRead(
        id=uuid4(),
        name="Test Org",
        domain="test.org",
        is_active=True,
        created_at=now,
        updated_at=now,
    )
    override_organization_service.get_user_organization.return_value = org

    response = client.get(
        app.url_path_for("get_member_profiles"),
    )

    assert response.status_code == 200
    data = response.json()
    assert len(data["members"]) == 2


def test_get_member_profiles_not_admin(client, override_organization_team_service):
    override_organization_team_service.get_team_member.return_value = (
        OrganizationMemberRead(
            id=uuid4(),
            user_id=uuid4(),
            organization_id=uuid4(),
            is_admin=False,
        )
    )
    response = client.get(
        app.url_path_for("get_member_profiles"),
    )
    assert response.status_code == 403
