from typing import Any
from uuid import UUID

from sqlalchemy.orm import Session

from app.core.database import BaseModel


class BaseRepository[T: BaseModel]:
    def __init__(self, db_session: Session, model: type[T]) -> None:
        self.db_session = db_session
        self.model = model

    def get_all(self) -> list[T]:
        """Get all instances of the model."""
        return self.db_session.query(self.model).all()

    def get_by_id(self, id: UUID) -> T | None:
        """Get an instance of the model by its ID."""
        return self.db_session.query(self.model).filter(self.model.id == id).first()

    def get_by_ids(self, ids: list[UUID]) -> list[T]:
        """Get instances of the model by a list of IDs."""
        if not ids:
            return []
        return self.db_session.query(self.model).filter(self.model.id.in_(ids)).all()

    def _get_by_attrs(self, **attrs: Any) -> list[T]:
        """Get instances of the model by attributes."""
        query = self.db_session.query(self.model)
        for key, value in attrs.items():
            if not hasattr(self.model, key):
                raise AttributeError(f"{self.model.__name__} has no attribute {key!r}")
            query = query.filter(getattr(self.model, key) == value)
        return query.all()

    def create(self, **kwargs: Any) -> T:
        """Create a new instance of the model."""
        instance = self.model()
        self._check_and_set_attrs(instance, **kwargs)
        self.db_session.add(instance)
        self.db_session.flush()
        return instance

    def update(self, id: UUID, **kwargs: Any) -> T:
        """Update an existing instance of the model."""
        instance = self.get_by_id(id)
        if not instance:
            raise ValueError(f"{self.model.__name__} with id {id} not found")
        self._check_and_set_attrs(instance, **kwargs)
        self.db_session.flush()
        return instance

    def _check_and_set_attrs(self, instance: T, **kwargs) -> None:
        """Check if the attributes exist and set them."""
        for key, value in kwargs.items():
            if not hasattr(instance, key):
                raise AttributeError(f"{self.model.__name__} has no attribute {key!r}")
            setattr(instance, key, value)

    def delete(self, id: UUID) -> None:
        """Delete an instance of the model by its ID."""
        instance = self.get_by_id(id)
        if not instance:
            raise ValueError(f"{self.model.__name__} with id {id} not found")
        self.db_session.delete(instance)
