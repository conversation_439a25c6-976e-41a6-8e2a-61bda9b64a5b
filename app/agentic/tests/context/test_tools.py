import asyncio

import pytest
from pydantic import BaseModel, Field

from app.agentic.context.schemas import (
    CreateContact,
    GetAccount,
    GetContact,
    GetOpportunity,
    ListContactsByAccount,
    ListOpportunitiesByAccount,
    SearchContacts,
    ToolDefinition,
    UpdateAccount,
    UpdateContact,
    UpdateOpportunity,
)
from app.agentic.context.tools import LinkupSearchInput, get_tools


def test_get_tools(
    mocker,
    user_id,
    mock_user_integrations_instance,
    mock_crm_provider,
):
    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"
    mock_linkup_tool.args_schema = LinkupSearchInput

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    mock_crm_provider.get_opportunity = mocker.MagicMock()
    mock_crm_provider.update_opportunity = mocker.MagicMock()
    mock_crm_provider.list_opportunities_by_account = mocker.MagicMock()
    mock_crm_provider.get_account = mocker.MagicMock()
    mock_crm_provider.update_account = mocker.MagicMock()
    mock_crm_provider.get_contact = mocker.MagicMock()
    mock_crm_provider.create_contact = mocker.MagicMock()
    mock_crm_provider.update_contact = mocker.MagicMock()
    mock_crm_provider.list_contacts_by_account = mocker.MagicMock()
    mock_crm_provider.search_contacts = mocker.MagicMock()

    tools = get_tools(user_id, mock_user_integrations_instance)

    mock_user_integrations_instance.crm.assert_called_once()

    assert len(tools) == 11

    tool_details = {
        "get_opportunity": (
            "Fetch a CRM opportunity by its ID",
            GetOpportunity,
            mock_crm_provider.get_opportunity,
        ),
        "update_opportunity": (
            "Update a CRM opportunity with provided fields",
            UpdateOpportunity,
            mock_crm_provider.update_opportunity,
        ),
        "list_opportunities_by_account": (
            "List CRM opportunities for a given account",
            ListOpportunitiesByAccount,
            mock_crm_provider.list_opportunities_by_account,
        ),
        "get_account": (
            "Fetch a CRM account by its ID",
            GetAccount,
            mock_crm_provider.get_account,
        ),
        "update_account": (
            "Update a CRM account with provided fields",
            UpdateAccount,
            mock_crm_provider.update_account,
        ),
        "get_contact": (
            "Fetch a CRM contact by its ID",
            GetContact,
            mock_crm_provider.get_contact,
        ),
        "create_contact": (
            "Create a new CRM contact",
            CreateContact,
            mock_crm_provider.create_contact,
        ),
        "update_contact": (
            "Update a CRM contact with provided fields",
            UpdateContact,
            mock_crm_provider.update_contact,
        ),
        "list_contacts_by_account": (
            "List CRM contacts for a given account",
            ListContactsByAccount,
            mock_crm_provider.list_contacts_by_account,
        ),
        "search_contacts": (
            "Search CRM contacts by criteria",
            SearchContacts,
            mock_crm_provider.search_contacts,
        ),
    }

    expected_names = list(tool_details.keys())
    actual_names = [tool.name for tool in tools]

    for expected_name in expected_names:
        assert expected_name in actual_names

    assert "search_web" in actual_names

    crm_tools = [tool for tool in tools if tool.name in tool_details]
    for tool in crm_tools:
        assert isinstance(tool, ToolDefinition)
        assert tool.name in tool_details
        expected_description, expected_schema, _ = tool_details[tool.name]
        assert tool.description == expected_description
        assert tool.args_schema == expected_schema
        assert tool.coroutine is not None
        assert asyncio.iscoroutinefunction(tool.coroutine)

    search_web_tool = next(tool for tool in tools if tool.name == "search_web")
    assert isinstance(search_web_tool, ToolDefinition)
    assert search_web_tool.name == "search_web"
    assert search_web_tool.description == "Search the web for information"
    assert search_web_tool.args_schema == LinkupSearchInput
    assert search_web_tool.coroutine is not None
    assert asyncio.iscoroutinefunction(search_web_tool.coroutine)


@pytest.mark.anyio
async def test_tool_functions(
    mocker,
    user_id,
    mock_crm_provider,
    mock_user_integrations_instance,
):
    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"

    class SearchWebSchema(BaseModel):
        query: str = Field(description="The search query")

    mock_linkup_tool.args_schema = SearchWebSchema

    mock_result = mocker.MagicMock()

    result_1 = mocker.MagicMock()
    result_1.name = "Test Result 1"
    result_1.url = "https://example.com/1"
    result_1.content = "This is test content 1"

    result_2 = mocker.MagicMock()
    result_2.name = "Test Result 2"
    result_2.url = "https://example.com/2"
    result_2.content = "This is test content 2"

    mock_result.results = [result_1, result_2]
    mock_linkup_tool.invoke.return_value = mock_result

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    tools = get_tools(user_id, mock_user_integrations_instance)

    tool_map = {t.name: t for t in tools}

    get_opportunity_tool = tool_map["get_opportunity"]
    update_opportunity_tool = tool_map["update_opportunity"]
    list_opportunities_by_account_tool = tool_map["list_opportunities_by_account"]
    get_account_tool = tool_map["get_account"]
    update_account_tool = tool_map["update_account"]
    get_contact_tool = tool_map["get_contact"]
    create_contact_tool = tool_map["create_contact"]
    update_contact_tool = tool_map["update_contact"]
    list_contacts_by_account_tool = tool_map["list_contacts_by_account"]
    search_contacts_tool = tool_map["search_contacts"]
    search_web_tool = tool_map["search_web"]

    mock_crm_provider.get_opportunity.reset_mock()
    mock_crm_provider.update_opportunity.reset_mock()
    mock_crm_provider.list_opportunities_by_account.reset_mock()
    mock_crm_provider.get_account.reset_mock()
    mock_crm_provider.update_account.reset_mock()
    mock_crm_provider.get_contact.reset_mock()
    mock_crm_provider.create_contact.reset_mock()
    mock_crm_provider.update_contact.reset_mock()
    mock_crm_provider.list_contacts_by_account.reset_mock()
    mock_crm_provider.search_contacts.reset_mock()

    await get_opportunity_tool.coroutine("001")
    mock_crm_provider.get_opportunity.assert_called_once_with("001")

    fields = {"Name": "Updated Opportunity"}
    await update_opportunity_tool.coroutine("001", fields)
    mock_crm_provider.update_opportunity.assert_called_once_with("001", fields)

    await list_opportunities_by_account_tool.coroutine("account123")
    mock_crm_provider.list_opportunities_by_account.assert_called_once_with(
        "account123"
    )

    await get_account_tool.coroutine("002")
    mock_crm_provider.get_account.assert_called_once_with("002")

    account_fields = {"Name": "Updated Account"}
    await update_account_tool.coroutine("002", account_fields)
    mock_crm_provider.update_account.assert_called_once_with("002", account_fields)

    await get_contact_tool.coroutine("003")
    mock_crm_provider.get_contact.assert_called_once_with("003")

    contact_data = {"FirstName": "John", "LastName": "Doe"}
    await create_contact_tool.coroutine(contact_data)
    mock_crm_provider.create_contact.assert_called_once_with(contact_data)

    contact_update_data = {"FirstName": "Jane"}
    await update_contact_tool.coroutine("003", contact_update_data)
    mock_crm_provider.update_contact.assert_called_once_with("003", contact_update_data)

    await list_contacts_by_account_tool.coroutine("002")
    mock_crm_provider.list_contacts_by_account.assert_called_once_with("002")

    search_criteria = {"Email": "<EMAIL>"}
    await search_contacts_tool.coroutine(search_criteria)
    mock_crm_provider.search_contacts.assert_called_once_with(search_criteria)
    result = await search_web_tool.coroutine("test query")
    mock_linkup_tool.invoke.assert_called_once_with({"query": "test query"})

    expected_result = (
        "Title: Test Result 1\nURL: https://example.com/1\nContent: This is test content 1\n\n\n"
        "Title: Test Result 2\nURL: https://example.com/2\nContent: This is test content 2\n"
    )
    assert result == expected_result


@pytest.mark.anyio
async def test_search_web_no_results(
    mocker,
    user_id,
    mock_user_integrations_instance,
):
    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"

    class SearchWebSchema(BaseModel):
        query: str = Field(description="The search query")

    mock_linkup_tool.args_schema = SearchWebSchema

    mock_result = mocker.MagicMock()
    mock_result.results = []
    mock_linkup_tool.invoke.return_value = mock_result

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    tools = get_tools(user_id, mock_user_integrations_instance)
    tool_map = {t.name: t for t in tools}
    search_web_tool = tool_map["search_web"]

    result = await search_web_tool.coroutine("no results query")
    mock_linkup_tool.invoke.assert_called_once_with({"query": "no results query"})
    assert result == "No search results found."


@pytest.mark.anyio
async def test_search_web_no_results_attribute(
    mocker,
    user_id,
    mock_user_integrations_instance,
):
    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"

    class SearchWebSchema(BaseModel):
        query: str = Field(description="The search query")

    mock_linkup_tool.args_schema = SearchWebSchema

    mock_result = mocker.MagicMock()
    del mock_result.results
    mock_linkup_tool.invoke.return_value = mock_result

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    tools = get_tools(user_id, mock_user_integrations_instance)
    tool_map = {t.name: t for t in tools}
    search_web_tool = tool_map["search_web"]

    result = await search_web_tool.coroutine("no results attribute query")
    mock_linkup_tool.invoke.assert_called_once_with(
        {"query": "no results attribute query"}
    )
    assert result == "No search results found."


def test_get_tools_no_integration(
    user_id,
    mock_user_integrations_instance,
) -> None:
    mock_user_integrations_instance.crm.return_value = None

    with pytest.raises(
        RuntimeError,
        match=f"No CRM integration configured for user {user_id}",
    ):
        get_tools(user_id, mock_user_integrations_instance)

    mock_user_integrations_instance.crm.assert_called_once()
