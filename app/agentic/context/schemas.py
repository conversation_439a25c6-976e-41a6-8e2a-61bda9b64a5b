from collections.abc import Awaitable, Callable
from typing import Any

from pydantic import BaseModel, ConfigDict, Field

from app.integrations.types import IntegrationSource


class ToolDefinition(BaseModel):
    name: str
    description: str
    coroutine: Callable[..., Awaitable[Any]]
    args_schema: type[BaseModel]

    model_config = ConfigDict(arbitrary_types_allowed=True)


class OrgConfig(BaseModel):
    integrations: dict[IntegrationSource, dict]

    model_config = ConfigDict(arbitrary_types_allowed=True)


class GetOpportunity(BaseModel):
    opportunity_id: str = Field(
        description="The Salesforce ID of the opportunity to retrieve."
    )


class UpdateOpportunity(BaseModel):
    opportunity_id: str = Field(
        description="The Salesforce ID of the opportunity to update."
    )
    fields: dict[str, Any] = Field(
        description="Dictionary of fields to update on the opportunity."
    )


class ListOpportunitiesByAccount(BaseModel):
    account_id: str = Field(
        description="The Salesforce ID of the account to list opportunities for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of opportunities to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of opportunities to skip before starting to return results.",
        ge=0,
    )


class GetAccount(BaseModel):
    account_id: str = Field(description="The Salesforce ID of the account to retrieve.")


class UpdateAccount(BaseModel):
    account_id: str = Field(description="The Salesforce ID of the account to update.")
    fields: dict[str, Any] = Field(
        description="Dictionary of fields to update on the account."
    )


class GetContact(BaseModel):
    contact_id: str = Field(description="The Salesforce ID of the contact to retrieve.")


class CreateContact(BaseModel):
    contact_data: dict[str, Any] = Field(
        description="Dictionary of fields to create the contact with."
    )


class UpdateContact(BaseModel):
    contact_id: str = Field(description="The Salesforce ID of the contact to update.")
    contact_data: dict[str, Any] = Field(
        description="Dictionary of fields to update on the contact."
    )


class ListContactsByAccount(BaseModel):
    account_id: str = Field(
        description="The Salesforce ID of the account to list contacts for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of contacts to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of contacts to skip before starting to return results.",
        ge=0,
    )


class SearchContacts(BaseModel):
    search_criteria: dict[str, Any] = Field(
        description="Dictionary of search criteria (e.g., {'Email': '<EMAIL>', 'FirstName': 'John'})."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of contacts to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of contacts to skip before starting to return results.",
        ge=0,
    )


class ListAccountAccess(BaseModel):
    crm_user_id: str = Field(
        description="The Salesforce ID of the user to list account access for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of account access records to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of account access records to skip before starting to return results.",
        ge=0,
    )
