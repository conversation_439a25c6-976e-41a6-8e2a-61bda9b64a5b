from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>
from langfuse.callback import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from psycopg.rows import dict_row
from psycopg_pool import AsyncConnectionPool

from app.agentic.graph.graph_manager import GraphManager
from app.core.config import config


@asynccontextmanager
async def lifespan(app: FastAPI):
    try:
        pool = AsyncConnectionPool(
            conninfo=str(config.database.database_url),
            min_size=1,
            max_size=5,
            kwargs={"row_factory": dict_row, "autocommit": True},
            open=False,
        )

        await pool.open()
        checkpointer = AsyncPostgresSaver(pool)  # type: ignore[arg-type]
        await checkpointer.setup()

        langfuse_handler = CallbackHandler(
            public_key=config.langfuse_public_key,
            secret_key=config.langfuse_secret_key,
            host=config.langfuse_host,
        )

        graph_manager = GraphManager(
            checkpointer=checkpointer, langfuse_handler=langfuse_handler
        )

        app.state.checkpoint_db_pool = pool
        app.state.graph_manager = graph_manager

        yield

    finally:
        if pool:
            await pool.close()

        app.state.checkpoint_db_pool = None
        app.state.graph_manager = None
