# ruff: noqa: S106

import pytest

from app.integrations.adapters.salesforce.client import SalesforceClientError
from app.integrations.adapters.salesforce.handler import (
    SalesforceHandler,
    SalesforceObjectType,
)
from app.integrations.base.credentials_resolver import (
    ICredentials,
)


@pytest.fixture
def mock_credentials(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "username": "<EMAIL>",
        "password": "password123",
        "security_token": "token123",
    }
    return mock_credentials


@pytest.fixture
def mock_oauth_credentials(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "access_token": "mock_access_token",
        "instance_url": "https://test.salesforce.com",
    }
    return mock_credentials


@pytest.fixture
def sfdc_handler(mocker, mock_credentials):
    mock_client = mocker.MagicMock()
    mocker.patch(
        "app.integrations.adapters.salesforce.refreshable_client_mixin.SalesforceClient",
        return_value=mock_client,
    )
    handler = SalesforceHandler(credentials=mock_credentials)
    assert handler.salesforce_client is mock_client
    return handler


def test_init_with_oauth_credentials(mocker, mock_oauth_credentials):
    mock_client = mocker.MagicMock()
    mocker.patch(
        "app.integrations.adapters.salesforce.refreshable_client_mixin.SalesforceClient",
        return_value=mock_client,
    )
    handler = SalesforceHandler(credentials=mock_oauth_credentials)
    from app.integrations.adapters.salesforce.refreshable_client_mixin import (
        SalesforceClient,
    )

    SalesforceClient.assert_called_once_with(
        instance_url="https://test.salesforce.com", access_token="mock_access_token"
    )
    assert handler.salesforce_client is mock_client


def test_init_with_missing_instance_url(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "access_token": "mock_access_token",
    }
    with pytest.raises(ValueError, match="Missing required 'instance_url'"):
        SalesforceHandler(credentials=mock_credentials)


def test_init_with_invalid_credentials(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "username": "<EMAIL>",
    }
    with pytest.raises(ValueError, match="Missing required Salesforce credentials"):
        SalesforceHandler(credentials=mock_credentials)


def test_client_creation_with_username_password(mocker, mock_credentials):
    client_mock = mocker.patch(
        "app.integrations.adapters.salesforce.refreshable_client_mixin.SalesforceClient"
    )
    SalesforceHandler(credentials=mock_credentials)
    client_mock.assert_called_once_with(
        username="<EMAIL>", password="password123", security_token="token123"
    )


def test_get_opportunity_success(sfdc_handler):
    expected_opportunity = {
        "Id": "006XXXXXXXXXXXX",
        "Name": "Test Opportunity",
        "Amount": 50000,
        "CloseDate": "2025-12-31",
        "StageName": "Prospecting",
    }
    sfdc_handler.salesforce_client.get_object.return_value = expected_opportunity
    result = sfdc_handler.get_opportunity("006XXXXXXXXXXXX")
    assert result == expected_opportunity
    sfdc_handler.salesforce_client.get_object.assert_called_once_with(
        SalesforceObjectType.OPPORTUNITY.value, "006XXXXXXXXXXXX"
    )


def test_get_opportunity_error(sfdc_handler):
    sfdc_handler.salesforce_client.get_object.side_effect = SalesforceClientError(
        "Record not found"
    )
    with pytest.raises(SalesforceClientError, match="Record not found"):
        sfdc_handler.get_opportunity("006XXXXXXXXXXXX")


def test_update_opportunity_success(sfdc_handler):
    opportunity_after = {
        "Id": "006XXXXXXXXXXXX",
        "Name": "Opportunity After Update",
        "Amount": 15000,
        "StageName": "Qualification",
    }
    sfdc_handler.salesforce_client.update_object.return_value = None
    sfdc_handler.salesforce_client.get_object.return_value = opportunity_after
    update_fields = {
        "Name": "Opportunity After Update",
        "Amount": 15000,
        "StageName": "Qualification",
    }
    result = sfdc_handler.update_opportunity("006XXXXXXXXXXXX", update_fields)
    assert result == opportunity_after
    sfdc_handler.salesforce_client.update_object.assert_called_once_with(
        SalesforceObjectType.OPPORTUNITY.value, "006XXXXXXXXXXXX", update_fields
    )
    sfdc_handler.salesforce_client.get_object.assert_called_once_with(
        SalesforceObjectType.OPPORTUNITY.value, "006XXXXXXXXXXXX"
    )


def test_update_opportunity_error(sfdc_handler):
    update_fields = {"StageName": "InvalidStage"}
    sfdc_handler.salesforce_client.update_object.side_effect = SalesforceClientError(
        "Invalid field value"
    )
    with pytest.raises(SalesforceClientError, match="Invalid field value"):
        sfdc_handler.update_opportunity("006XXXXXXXXXXXX", update_fields)


def test_list_opportunities_by_account_success(sfdc_handler):
    expected_opportunities = [
        {
            "Id": "006XXXXXXXXXXXX",
            "Name": "Opportunity 1",
            "Amount": 10000,
        },
        {
            "Id": "006YYYYYYYYYYYY",
            "Name": "Opportunity 2",
            "Amount": 20000,
        },
    ]
    sfdc_handler.salesforce_client.list_objects.return_value = expected_opportunities
    result = sfdc_handler.list_opportunities_by_account(
        account_id="001XXXXXXXXXXXX",
        fields="ALL",
        limit=10,
        offset=0,
    )
    assert result == expected_opportunities
    sfdc_handler.salesforce_client.list_objects.assert_called_once_with(
        object_type=SalesforceObjectType.OPPORTUNITY.value,
        fields="ALL",
        where_clause="AccountId = '001XXXXXXXXXXXX'",
        order_by=None,
        limit=10,
        offset=0,
    )


def test_list_opportunities_by_account_empty(sfdc_handler):
    result = sfdc_handler.list_opportunities_by_account(account_id="")
    assert result == []
    sfdc_handler.salesforce_client.list_objects.assert_not_called()


def test_list_opportunities_by_account_error(sfdc_handler):
    sfdc_handler.salesforce_client.list_objects.side_effect = SalesforceClientError(
        "Failed to execute query"
    )
    with pytest.raises(SalesforceClientError, match="Failed to execute query"):
        sfdc_handler.list_opportunities_by_account("001XXXXXXXXXXXX")


def test_get_account_success(sfdc_handler):
    expected_account = {
        "Id": "001XXXXXXXXXXXX",
        "Name": "Test Account",
        "Industry": "Technology",
    }
    sfdc_handler.salesforce_client.get_object.return_value = expected_account
    result = sfdc_handler.get_account("001XXXXXXXXXXXX")
    assert result == expected_account
    sfdc_handler.salesforce_client.get_object.assert_called_once_with(
        SalesforceObjectType.ACCOUNT.value, "001XXXXXXXXXXXX"
    )


def test_get_account_error(sfdc_handler):
    sfdc_handler.salesforce_client.get_object.side_effect = SalesforceClientError(
        "Record not found"
    )
    with pytest.raises(SalesforceClientError, match="Record not found"):
        sfdc_handler.get_account("001XXXXXXXXXXXX")


def test_update_account_success(sfdc_handler):
    account_after = {
        "Id": "001XXXXXXXXXXXX",
        "Name": "Account After Update",
        "Industry": "Technology",
    }
    sfdc_handler.salesforce_client.update_object.return_value = None
    sfdc_handler.salesforce_client.get_object.return_value = account_after
    update_fields = {
        "Name": "Account After Update",
        "Industry": "Technology",
    }
    result = sfdc_handler.update_account("001XXXXXXXXXXXX", update_fields)
    assert result == account_after
    sfdc_handler.salesforce_client.update_object.assert_called_once_with(
        SalesforceObjectType.ACCOUNT.value, "001XXXXXXXXXXXX", update_fields
    )
    sfdc_handler.salesforce_client.get_object.assert_called_once_with(
        SalesforceObjectType.ACCOUNT.value, "001XXXXXXXXXXXX"
    )


def test_update_account_error(sfdc_handler):
    update_fields = {"Industry": "InvalidIndustry"}
    sfdc_handler.salesforce_client.update_object.side_effect = SalesforceClientError(
        "Invalid field value"
    )
    with pytest.raises(SalesforceClientError, match="Invalid field value"):
        sfdc_handler.update_account("001XXXXXXXXXXXX", update_fields)


def test_resolve_account_access(mocker, sfdc_handler):
    from app.integrations.schemas import CRMAccountAccessData

    mock_access_data = [
        CRMAccountAccessData(
            account_id="001XXXXXXXXXXXX",
            account_name="Account 1",
            access_type="owner",
            access_role=None,
        )
    ]
    mock_resolver = mocker.MagicMock()
    mock_resolver.get_user_account_access.return_value = mock_access_data
    mocker.patch(
        "app.integrations.adapters.salesforce.handler.SalesforceAccountAccessResolver",
        return_value=mock_resolver,
    )
    result = sfdc_handler.resolve_account_access(salesforce_user_id="005XXXXXXXXXXXX")
    from app.integrations.adapters.salesforce.handler import (
        SalesforceAccountAccessResolver,
    )

    SalesforceAccountAccessResolver.assert_called_once_with(
        client=sfdc_handler.salesforce_client
    )
    mock_resolver.get_user_account_access.assert_called_once_with("005XXXXXXXXXXXX")
    assert result == mock_access_data


def test_get_contact_success(sfdc_handler):
    expected_contact = {
        "Id": "003XXXXXXXXXXXX",
        "FirstName": "John",
        "LastName": "Doe",
        "Email": "<EMAIL>",
    }
    sfdc_handler.salesforce_client.get_object.return_value = expected_contact
    result = sfdc_handler.get_contact("003XXXXXXXXXXXX")
    assert result == expected_contact
    sfdc_handler.salesforce_client.get_object.assert_called_once_with(
        SalesforceObjectType.CONTACT.value, "003XXXXXXXXXXXX"
    )


def test_get_contact_error(sfdc_handler):
    sfdc_handler.salesforce_client.get_object.side_effect = SalesforceClientError(
        "Record not found"
    )
    with pytest.raises(SalesforceClientError, match="Record not found"):
        sfdc_handler.get_contact("003XXXXXXXXXXXX")


def test_create_contact_success(sfdc_handler):
    contact_data = {
        "FirstName": "Jane",
        "LastName": "Smith",
        "Email": "<EMAIL>",
    }
    create_result = {"success": True, "id": "003YYYYYYYYYYYY"}
    contact_after = {
        "Id": "003YYYYYYYYYYYY",
        "FirstName": "Jane",
        "LastName": "Smith",
        "Email": "<EMAIL>",
    }

    sfdc_handler.salesforce_client.create_object.return_value = create_result
    sfdc_handler.salesforce_client.get_object.return_value = contact_after

    result = sfdc_handler.create_contact(contact_data)

    assert result == contact_after
    sfdc_handler.salesforce_client.create_object.assert_called_once_with(
        SalesforceObjectType.CONTACT.value, contact_data
    )
    sfdc_handler.salesforce_client.get_object.assert_called_once_with(
        SalesforceObjectType.CONTACT.value, "003YYYYYYYYYYYY"
    )


def test_create_contact_error(sfdc_handler):
    contact_data = {"FirstName": "Jane"}
    sfdc_handler.salesforce_client.create_object.side_effect = SalesforceClientError(
        "Required field missing"
    )
    with pytest.raises(SalesforceClientError, match="Required field missing"):
        sfdc_handler.create_contact(contact_data)


def test_update_contact_success(sfdc_handler):
    contact_after = {
        "Id": "003XXXXXXXXXXXX",
        "FirstName": "John",
        "LastName": "Updated",
        "Email": "<EMAIL>",
    }
    sfdc_handler.salesforce_client.update_object.return_value = None
    sfdc_handler.salesforce_client.get_object.return_value = contact_after
    update_fields = {
        "LastName": "Updated",
        "Email": "<EMAIL>",
    }
    result = sfdc_handler.update_contact("003XXXXXXXXXXXX", update_fields)
    assert result == contact_after
    sfdc_handler.salesforce_client.update_object.assert_called_once_with(
        SalesforceObjectType.CONTACT.value, "003XXXXXXXXXXXX", update_fields
    )
    sfdc_handler.salesforce_client.get_object.assert_called_once_with(
        SalesforceObjectType.CONTACT.value, "003XXXXXXXXXXXX"
    )


def test_update_contact_error(sfdc_handler):
    update_fields = {"Email": "invalid-email"}
    sfdc_handler.salesforce_client.update_object.side_effect = SalesforceClientError(
        "Invalid email format"
    )
    with pytest.raises(SalesforceClientError, match="Invalid email format"):
        sfdc_handler.update_contact("003XXXXXXXXXXXX", update_fields)


def test_list_contacts_by_account_success(sfdc_handler):
    expected_contacts = [
        {
            "Id": "003XXXXXXXXXXXX",
            "FirstName": "John",
            "LastName": "Doe",
        },
        {
            "Id": "003YYYYYYYYYYYY",
            "FirstName": "Jane",
            "LastName": "Smith",
        },
    ]
    sfdc_handler.salesforce_client.list_objects.return_value = expected_contacts
    result = sfdc_handler.list_contacts_by_account(
        account_id="001XXXXXXXXXXXX",
        fields="ALL",
        limit=10,
        offset=0,
    )
    assert result == expected_contacts
    sfdc_handler.salesforce_client.list_objects.assert_called_once_with(
        object_type=SalesforceObjectType.CONTACT.value,
        fields="ALL",
        where_clause="AccountId = '001XXXXXXXXXXXX'",
        order_by=None,
        limit=10,
        offset=0,
    )


def test_list_contacts_by_account_empty(sfdc_handler):
    result = sfdc_handler.list_contacts_by_account(account_id="")
    assert result == []
    sfdc_handler.salesforce_client.list_objects.assert_not_called()


def test_search_contacts_success(sfdc_handler):
    expected_contacts = [
        {
            "Id": "003XXXXXXXXXXXX",
            "FirstName": "John",
            "LastName": "Doe",
            "Email": "<EMAIL>",
        }
    ]
    sfdc_handler.salesforce_client.list_objects.return_value = expected_contacts
    search_criteria = {"Email": "<EMAIL>", "FirstName": "John"}
    result = sfdc_handler.search_contacts(search_criteria, limit=10, offset=0)
    assert result == expected_contacts
    sfdc_handler.salesforce_client.list_objects.assert_called_once_with(
        object_type=SalesforceObjectType.CONTACT.value,
        fields=None,
        where_clause="Email = '<EMAIL>' AND FirstName LIKE '%John%'",
        order_by=None,
        limit=10,
        offset=0,
    )


def test_search_contacts_empty_criteria(sfdc_handler):
    result = sfdc_handler.search_contacts({})
    assert result == []
    sfdc_handler.salesforce_client.list_objects.assert_not_called()
