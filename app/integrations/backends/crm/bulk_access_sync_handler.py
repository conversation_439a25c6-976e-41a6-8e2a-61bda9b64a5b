import uuid
from collections.abc import Callable
from typing import Any

from app.common.helpers.logger import get_logger
from app.common.pipeline.runner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.integrations.backends.crm.bulk_access_sync_stage import AccountAccessSyncStage
from app.integrations.backends.crm.bulk_access_synchronizer import (
    AccountAccessSynchronizer,
)
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.stores.pg_crm_store import PostgresCRMStore
from app.integrations.types import IntegrationSource

logger = get_logger()


class BulkAccountAccessSyncHandler:
    def __init__(
        self,
        tenant_id: uuid.UUID,
        source: IntegrationSource,
        db_session_factory: Callable,
        adapter_factory: Callable,
        crm_store: PostgresCRMStore,
        credentials_resolver: ICredentialsResolver | None = None,
    ):
        self._tenant_id = tenant_id
        self._source = source
        self._db_session_factory = db_session_factory
        self._adapter_factory = adapter_factory
        self._crm_store = crm_store
        self._credentials_resolver = credentials_resolver
        self._synchronizer_cache: dict[int, AccountAccessSynchronizer] = {}

    def execute(
        self,
        crm_user_ids: list[str],
        get_credentials_resolver: Callable[[str], ICredentialsResolver] | None = None,
        interval_seconds: int = 300,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        logger.info(f"Starting CRM account access sync for tenant {self._tenant_id}")

        if get_credentials_resolver is None and self._credentials_resolver is None:
            error_msg = "Either get_credentials_resolver must be provided or credentials_resolver must be set in constructor"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if get_credentials_resolver is None:

            def default_get_credentials_resolver(_):
                return self._credentials_resolver

            get_credentials_resolver = default_get_credentials_resolver

        users_and_synchronizers = []
        failed_users = []

        for user_id in crm_user_ids:
            user_resolver = get_credentials_resolver(user_id)

            if user_resolver is None:
                error_msg = f"Missing credentials resolver for user: {user_id}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            try:
                synchronizer = self._get_or_create_synchronizer(user_resolver)

                if synchronizer is not None:
                    users_and_synchronizers.append((user_id, synchronizer))
                else:
                    logger.warning(
                        f"Failed to create synchronizer for user {user_id} due to invalid configuration"
                    )
                    failed_users.append(user_id)
            except Exception as e:
                logger.warning(
                    f"Error creating synchronizer for user {user_id}: {str(e)}"
                )
                failed_users.append(user_id)

        if failed_users:
            logger.warning(
                f"Could not create synchronizers for users: {', '.join(failed_users)}"
            )

        if not users_and_synchronizers:
            logger.error("No valid synchronizers could be created")
            raise ValueError("No valid synchronizers could be created")

        sync_stage = AccountAccessSyncStage(
            tenant_id=self._tenant_id,
            source=self._source,
            db_session=self._db_session_factory(),
            user_synchronizers=users_and_synchronizers,
            interval_seconds=interval_seconds,
            stage_id=f"crm_account_access_sync_for_{self._tenant_id}",
        )

        # Create and configure the pipeline
        pipeline = PipelineRunner()
        pipeline.add_stage(sync_stage)

        # Execute the pipeline
        if daemon_mode:
            logger.info("Starting account access sync pipeline in daemon mode")
            pipeline.start_daemon()
            return {"status": "daemon_stopped"}
        else:
            logger.info("Running account access sync pipeline once")
            results = pipeline.run()
            logger.info("Account access sync pipeline execution completed")
            return results

    def _get_or_create_synchronizer(
        self, resolver: ICredentialsResolver
    ) -> AccountAccessSynchronizer | None:
        resolver_id = id(resolver)

        if resolver_id in self._synchronizer_cache:
            return self._synchronizer_cache[resolver_id]

        synchronizer = self._create_synchronizer(resolver)

        if synchronizer is not None:
            self._synchronizer_cache[resolver_id] = synchronizer

        return synchronizer

    def _create_synchronizer(
        self,
        creds_resolver: ICredentialsResolver,
    ) -> AccountAccessSynchronizer | None:
        if creds_resolver is None:
            return None

        try:
            credentials = creds_resolver.get_credentials(self._source)
            adapter = self._adapter_factory(credentials)

            return AccountAccessSynchronizer(
                tenant_id=self._tenant_id,
                crm_store=self._crm_store,
                adapter=adapter,
            )

        except Exception:
            logger.exception("Error creating synchronizer")
            return None
