from typing import Any

from app.integrations.adapters.salesforce.handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.base.crm_adapter import BaseCRMAdapter
from app.integrations.schemas import CRMAccountAccessData
from app.integrations.types import IntegrationSource


class SalesforceAdapter(BaseCRMAdapter):
    def __init__(self, credentials: ICredentials):
        super().__init__(credentials)
        self._handler = SalesforceHandler(credentials=credentials)

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.SALESFORCE

    def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return self._handler.get_opportunity(opportunity_id)

    def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        return self._handler.update_opportunity(opportunity_id, fields)

    def list_opportunities_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return self._handler.list_opportunities_by_account(
            account_id=account_id, limit=limit, offset=offset
        )

    def get_account(self, account_id: str) -> dict[str, Any]:
        return self._handler.get_account(account_id)

    def update_account(self, account_id: str, fields: dict[str, Any]) -> dict[str, Any]:
        return self._handler.update_account(account_id, fields)

    def resolve_account_access(self, crm_user_id: str) -> list[CRMAccountAccessData]:
        return self._handler.resolve_account_access(salesforce_user_id=crm_user_id)

    def get_contact(self, contact_id: str) -> dict[str, Any]:
        return self._handler.get_contact(contact_id)

    def create_contact(self, contact_data: dict[str, Any]) -> dict[str, Any]:
        return self._handler.create_contact(contact_data)

    def update_contact(
        self, contact_id: str, contact_data: dict[str, Any]
    ) -> dict[str, Any]:
        return self._handler.update_contact(contact_id, contact_data)

    def list_contacts_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return self._handler.list_contacts_by_account(
            account_id=account_id, limit=limit, offset=offset
        )

    def search_contacts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return self._handler.search_contacts(
            search_criteria=search_criteria, limit=limit, offset=offset
        )
