from enum import Enum
from typing import Any, Literal

from app.common.helpers.logger import get_logger
from app.integrations.adapters.salesforce.access_resolver import (
    SalesforceAccountAccessResolver,
)
from app.integrations.adapters.salesforce.refreshable_client_mixin import (
    SalesforceRefreshableClientMixin,
)
from app.integrations.base.credentials_resolver import (
    ICredentials,
)
from app.integrations.schemas import CRMAccountAccessData

logger = get_logger()


class SalesforceObjectType(str, Enum):
    ACCOUNT = "Account"
    OPPORTUNITY = "Opportunity"
    CONTACT = "Contact"


class SalesforceHandler(SalesforceRefreshableClientMixin):
    def __init__(self, credentials: ICredentials):
        self.init_salesforce_client(credentials)

    @SalesforceRefreshableClientMixin.handle_expired_session
    def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return self.salesforce_client.get_object(
            SalesforceObjectType.OPPORTUNITY.value, opportunity_id
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        self.salesforce_client.update_object(
            SalesforceObjectType.OPPORTUNITY.value, opportunity_id, fields
        )

        updated_opportunity = self.salesforce_client.get_object(
            SalesforceObjectType.OPPORTUNITY.value, opportunity_id
        )

        return updated_opportunity

    @SalesforceRefreshableClientMixin.handle_expired_session
    def list_opportunities_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        where_clause = f"AccountId = '{account_id}'"

        return self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.OPPORTUNITY.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    def get_account(self, account_id: str) -> dict[str, Any]:
        return self.salesforce_client.get_object(
            SalesforceObjectType.ACCOUNT.value, account_id
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    def update_account(self, account_id: str, fields: dict[str, Any]) -> dict[str, Any]:
        self.salesforce_client.update_object(
            SalesforceObjectType.ACCOUNT.value, account_id, fields
        )

        updated_account = self.salesforce_client.get_object(
            SalesforceObjectType.ACCOUNT.value, account_id
        )

        return updated_account

    @SalesforceRefreshableClientMixin.handle_expired_session
    def resolve_account_access(
        self, salesforce_user_id: str
    ) -> list[CRMAccountAccessData]:
        resolver = SalesforceAccountAccessResolver(client=self.salesforce_client)
        return resolver.get_user_account_access(salesforce_user_id)

    @SalesforceRefreshableClientMixin.handle_expired_session
    def get_contact(self, contact_id: str) -> dict[str, Any]:
        return self.salesforce_client.get_object(
            SalesforceObjectType.CONTACT.value, contact_id
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    def create_contact(self, contact_data: dict[str, Any]) -> dict[str, Any]:
        result = self.salesforce_client.create_object(
            SalesforceObjectType.CONTACT.value, contact_data
        )

        if result.get("success") and result.get("id"):
            return self.get_contact(result["id"])

        return result

    @SalesforceRefreshableClientMixin.handle_expired_session
    def update_contact(
        self, contact_id: str, contact_data: dict[str, Any]
    ) -> dict[str, Any]:
        self.salesforce_client.update_object(
            SalesforceObjectType.CONTACT.value, contact_id, contact_data
        )

        updated_contact = self.salesforce_client.get_object(
            SalesforceObjectType.CONTACT.value, contact_id
        )

        return updated_contact

    @SalesforceRefreshableClientMixin.handle_expired_session
    def list_contacts_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        where_clause = f"AccountId = '{account_id}'"

        return self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.CONTACT.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    @SalesforceRefreshableClientMixin.handle_expired_session
    def search_contacts(
        self,
        search_criteria: dict[str, Any],
        fields: list[str] | Literal["ALL"] | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        where_conditions = []

        for field, value in search_criteria.items():
            if value:
                if field.lower() in ["firstname", "lastname", "name"]:
                    where_conditions.append(f"{field} LIKE '%{value}%'")
                elif field.lower() == "email":
                    where_conditions.append(f"Email = '{value}'")
                elif field.lower() == "phone":
                    where_conditions.append(f"Phone = '{value}'")
                else:
                    where_conditions.append(f"{field} = '{value}'")

        if not where_conditions:
            return []

        where_clause = " AND ".join(where_conditions)

        return self.salesforce_client.list_objects(
            object_type=SalesforceObjectType.CONTACT.value,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )
